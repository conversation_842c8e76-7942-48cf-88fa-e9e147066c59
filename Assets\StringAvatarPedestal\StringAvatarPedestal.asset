%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c333ccfdd0cbdbc4ca30cef2dd6e6b9b, type: 3}
  m_Name: StringAvatarPedestal
  m_EditorClassIdentifier: 
  serializedUdonProgramAsset: {fileID: 11400000, guid: 4669d47b91f49fd43bf2bfd78bbdd6d2,
    type: 2}
  udonAssembly: 
  assemblyError: 
  sourceCsScript: {fileID: 11500000, guid: eaa4cf689b719e84f955869d4e7fc695, type: 3}
  scriptVersion: 2
  compiledVersion: 2
  behaviourSyncMode: 0
  hasInteractEvent: 0
  scriptID: -7330033112111965671
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: fieldDefinitions
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[UdonSharp.Compiler.FieldDefinition,
        UdonSharp.Editor]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 20
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarPedestalPrefab
    - Name: $v
      Entry: 7
      Data: 2|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarPedestalPrefab
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 3|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.GameObject, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 4|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 5|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Pedestal Configuration
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: placementDirection
    - Name: $v
      Entry: 7
      Data: 6|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: placementDirection
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 7|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: PlacementDirection, Assembly-CSharp
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 7
      Data: 8|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Int32, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 9|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: pedestalSpacing
    - Name: $v
      Entry: 7
      Data: 10|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: pedestalSpacing
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 11|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Single, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 12|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: showAvatarInfo
    - Name: $v
      Entry: 7
      Data: 13|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: showAvatarInfo
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 14|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Boolean, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 14
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 15|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: gridWidth
    - Name: $v
      Entry: 7
      Data: 16|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: gridWidth
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 17|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 18|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Grid Layout (only for Grid direction)
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: gridSpacingZ
    - Name: $v
      Entry: 7
      Data: 19|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: gridSpacingZ
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 20|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: chunkSize
    - Name: $v
      Entry: 7
      Data: 21|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: chunkSize
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 22|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 23|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Performance Settings
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: chunkDelay
    - Name: $v
      Entry: 7
      Data: 24|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: chunkDelay
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 25|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: updateInterval
    - Name: $v
      Entry: 7
      Data: 26|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: updateInterval
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 27|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 28|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Update Settings
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: mainUrl
    - Name: $v
      Entry: 7
      Data: 29|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: mainUrl
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 30|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: VRC.SDKBase.VRCUrl, VRCSDKBase
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 30
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 31|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 32|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Data Source
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 33|UnityEngine.SerializeField, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarInfoPrefab
    - Name: $v
      Entry: 7
      Data: 34|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarInfoPrefab
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 35|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 36|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Display Options
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: pedestalParent
    - Name: $v
      Entry: 7
      Data: 37|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: pedestalParent
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 38|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.Transform, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 38
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 39|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarPedestals
    - Name: $v
      Entry: 7
      Data: 40|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarPedestals
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 41|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: VRC.SDK3.Components.VRCAvatarPedestal[], VRCSDK3
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 41
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 42|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarInfoDisplays
    - Name: $v
      Entry: 7
      Data: 43|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarInfoDisplays
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 44|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.GameObject[], UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 44
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 45|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarData
    - Name: $v
      Entry: 7
      Data: 46|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarData
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 47|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.String[], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 47
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 48|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: downloaded
    - Name: $v
      Entry: 7
      Data: 49|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: downloaded
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 14
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 14
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 50|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: isWaiting
    - Name: $v
      Entry: 7
      Data: 51|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: isWaiting
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 14
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 14
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 52|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: pedestalsCreated
    - Name: $v
      Entry: 7
      Data: 53|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: pedestalsCreated
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 14
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 14
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 54|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: totalAvatars
    - Name: $v
      Entry: 7
      Data: 55|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: totalAvatars
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 56|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: currentChunk
    - Name: $v
      Entry: 7
      Data: 57|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: currentChunk
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 58|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
