%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c333ccfdd0cbdbc4ca30cef2dd6e6b9b, type: 3}
  m_Name: StringAvatarPedestal
  m_EditorClassIdentifier: 
  serializedUdonProgramAsset: {fileID: 11400000, guid: 4669d47b91f49fd43bf2bfd78bbdd6d2,
    type: 2}
  udonAssembly: 
  assemblyError: 
  sourceCsScript: {fileID: 11500000, guid: eaa4cf689b719e84f955869d4e7fc695, type: 3}
  scriptVersion: 2
  compiledVersion: 2
  behaviourSyncMode: 1
  hasInteractEvent: 0
  scriptID: -7330033112111965671
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: fieldDefinitions
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[UdonSharp.Compiler.FieldDefinition,
        UdonSharp.Editor]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 48
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarPedestalPrefab
    - Name: $v
      Entry: 7
      Data: 2|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarPedestalPrefab
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 3|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.GameObject, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 4|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 5|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Main Row Configuration
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: placementDirection
    - Name: $v
      Entry: 7
      Data: 6|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: placementDirection
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 7|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: PlacementDirection, Assembly-CSharp
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 7
      Data: 8|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Int32, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 9|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: pedestalSpacing
    - Name: $v
      Entry: 7
      Data: 10|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: pedestalSpacing
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 11|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Single, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 12|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: stackHeight
    - Name: $v
      Entry: 7
      Data: 13|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: stackHeight
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 14|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: stackSpacing
    - Name: $v
      Entry: 7
      Data: 15|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: stackSpacing
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 16|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: pedestalScale
    - Name: $v
      Entry: 7
      Data: 17|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: pedestalScale
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 18|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: tagNames
    - Name: $v
      Entry: 7
      Data: 19|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: tagNames
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 20|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.String[], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 20
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 21|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 22|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Tag-Based Prefab System
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 23|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Tag names to look for (e.g., 'FISH', 'ROSE_FISH', 'VAPOR_FISH')
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: tagPrefabs
    - Name: $v
      Entry: 7
      Data: 24|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: tagPrefabs
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 25|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.GameObject[], UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 26|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 27|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Prefabs to spawn for each tag (must match tagNames array order)
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: tagPriorities
    - Name: $v
      Entry: 7
      Data: 28|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: tagPriorities
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 29|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Int32[], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 29
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 30|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 31|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Priority for each tag (higher = higher priority, must match tagNames
        array order)
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: tagOffsets
    - Name: $v
      Entry: 7
      Data: 32|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: tagOffsets
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 33|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.Vector3[], UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 33
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 34|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 35|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Offset position for each tag prefab (must match tagNames array order)
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: tagScales
    - Name: $v
      Entry: 7
      Data: 36|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: tagScales
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 37|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Single[], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 37
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 38|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 39|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Scale for each tag prefab (must match tagNames array order)
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: datePrefab
    - Name: $v
      Entry: 7
      Data: 40|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: datePrefab
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 3
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 41|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 42|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Date Display System
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 43|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Prefab with TextMeshPro component to display creation date
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: dateOffset
    - Name: $v
      Entry: 7
      Data: 44|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: dateOffset
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 45|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.Vector3, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 45
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 46|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 47|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Offset position for date display relative to pedestal
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: dateScale
    - Name: $v
      Entry: 7
      Data: 48|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: dateScale
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 49|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 50|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Scale multiplier for date display
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: dateFormat
    - Name: $v
      Entry: 7
      Data: 51|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: dateFormat
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 52|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.String, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 52
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 53|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 54|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Date format (e.g., 'MMM dd, yyyy' for 'Jun 25, 2025')
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: enableRandomRow
    - Name: $v
      Entry: 7
      Data: 55|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: enableRandomRow
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 56|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Boolean, mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 57|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 58|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Random Row Configuration
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowCount
    - Name: $v
      Entry: 7
      Data: 59|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowCount
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 60|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowDirection
    - Name: $v
      Entry: 7
      Data: 61|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowDirection
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 7
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 62|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowSpacing
    - Name: $v
      Entry: 7
      Data: 63|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowSpacing
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 64|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowStackHeight
    - Name: $v
      Entry: 7
      Data: 65|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowStackHeight
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 66|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowStackSpacing
    - Name: $v
      Entry: 7
      Data: 67|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowStackSpacing
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 68|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowScale
    - Name: $v
      Entry: 7
      Data: 69|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowScale
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 70|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: chunkSize
    - Name: $v
      Entry: 7
      Data: 71|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: chunkSize
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 72|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 73|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Performance Settings
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: chunkDelay
    - Name: $v
      Entry: 7
      Data: 74|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: chunkDelay
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 11
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 75|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: mainUrl
    - Name: $v
      Entry: 7
      Data: 76|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: mainUrl
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 77|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: VRC.SDKBase.VRCUrl, VRCSDKBase
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 77
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 78|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 79|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Data Source
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 80|UnityEngine.SerializeField, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: pedestalParent
    - Name: $v
      Entry: 7
      Data: 81|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: pedestalParent
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 82|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: UnityEngine.Transform, UnityEngine.CoreModule
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 82
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 83|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 84|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Display Options
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowParent
    - Name: $v
      Entry: 7
      Data: 85|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowParent
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 82
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 82
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 86|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: tagPrefabParent
    - Name: $v
      Entry: 7
      Data: 87|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: tagPrefabParent
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 82
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 82
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 88|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarCountText
    - Name: $v
      Entry: 7
      Data: 89|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarCountText
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 90|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: TMPro.TextMeshProUGUI, Unity.TextMeshPro
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 90
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 91|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: enableRandomizeButton
    - Name: $v
      Entry: 7
      Data: 92|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: enableRandomizeButton
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: true
    - Name: _fieldAttributes
      Entry: 7
      Data: 93|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 94|UnityEngine.HeaderAttribute, UnityEngine.CoreModule
    - Name: header
      Entry: 1
      Data: Randomize Controls
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 95|UnityEngine.TooltipAttribute, UnityEngine.CoreModule
    - Name: tooltip
      Entry: 1
      Data: Call the 'RandomizeRandomRow' method from a UI button or other trigger
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarPedestals
    - Name: $v
      Entry: 7
      Data: 96|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarPedestals
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 97|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: VRC.SDK3.Components.VRCAvatarPedestal[], VRCSDK3
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 97
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 98|System.Collections.Generic.List`1[[System.Attribute, mscorlib]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowPedestals
    - Name: $v
      Entry: 7
      Data: 99|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowPedestals
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 97
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 97
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 100|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: mainRowTagPrefabs
    - Name: $v
      Entry: 7
      Data: 101|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: mainRowTagPrefabs
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 102|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowTagPrefabs
    - Name: $v
      Entry: 7
      Data: 103|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowTagPrefabs
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 104|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: mainRowDateDisplays
    - Name: $v
      Entry: 7
      Data: 105|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: mainRowDateDisplays
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 106|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowDateDisplays
    - Name: $v
      Entry: 7
      Data: 107|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowDateDisplays
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 25
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 108|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarData
    - Name: $v
      Entry: 7
      Data: 109|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarData
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 20
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 20
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 110|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: isRoseFishAvatar
    - Name: $v
      Entry: 7
      Data: 111|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: isRoseFishAvatar
    - Name: <UserType>k__BackingField
      Entry: 7
      Data: 112|System.RuntimeType, mscorlib
    - Name: 
      Entry: 1
      Data: System.Boolean[], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 112
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 113|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: avatarHighestTagIndex
    - Name: $v
      Entry: 7
      Data: 114|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: avatarHighestTagIndex
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 29
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 29
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 115|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: sortedIndices
    - Name: $v
      Entry: 7
      Data: 116|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: sortedIndices
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 29
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 29
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 117|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomIndices
    - Name: $v
      Entry: 7
      Data: 118|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomIndices
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 29
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 29
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 119|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: downloaded
    - Name: $v
      Entry: 7
      Data: 120|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: downloaded
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 121|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: pedestalsCreated
    - Name: $v
      Entry: 7
      Data: 122|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: pedestalsCreated
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 123|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowCreated
    - Name: $v
      Entry: 7
      Data: 124|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowCreated
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 56
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 125|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: totalAvatars
    - Name: $v
      Entry: 7
      Data: 126|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: totalAvatars
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 127|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: roseFishCount
    - Name: $v
      Entry: 7
      Data: 128|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: roseFishCount
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 129|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: currentChunk
    - Name: $v
      Entry: 7
      Data: 130|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: currentChunk
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 131|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: randomRowChunk
    - Name: $v
      Entry: 7
      Data: 132|UdonSharp.Compiler.FieldDefinition, UdonSharp.Editor
    - Name: <Name>k__BackingField
      Entry: 1
      Data: randomRowChunk
    - Name: <UserType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SystemType>k__BackingField
      Entry: 9
      Data: 8
    - Name: <SyncMode>k__BackingField
      Entry: 7
      Data: System.Nullable`1[[UdonSharp.UdonSyncMode, UdonSharp.Runtime]], mscorlib
    - Name: 
      Entry: 6
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: <IsSerialized>k__BackingField
      Entry: 5
      Data: false
    - Name: _fieldAttributes
      Entry: 7
      Data: 133|System.Collections.Generic.List`1[[System.Attribute, mscorlib]],
        mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
