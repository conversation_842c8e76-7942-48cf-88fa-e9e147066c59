using UdonSharp;
using UnityEngine;
using VRC.SDKBase;

namespace TheZiver
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class RandomizeButtonHandler : UdonSharpBehaviour
    {
        [Header("References")]
        [Tooltip("The StringAvatarPedestal script to control")]
        public StringAvatarPedestal avatarSystem;

        [<PERSON><PERSON>("Interaction Settings")]
        [<PERSON>lt<PERSON>("Cooldown time in seconds between randomizations")]
        public float cooldownTime = 2f;

        [<PERSON>ltip("Show debug messages in console")]
        public bool showDebugMessages = true;

        // Private variables
        private bool isLoading = false;
        private float lastRandomizeTime = 0f;

        void Start()
        {
            // Validate references
            if (avatarSystem == null)
            {
                Debug.LogError("[RandomizeButtonHandler] Avatar System reference is missing!");
            }
        }

        public override void Interact()
        {
            VRCPlayerApi localPlayer = Networking.LocalPlayer;

            if (localPlayer == null)
            {
                if (showDebugMessages)
                    Debug.LogWarning("[RandomizeButtonHandler] Local player is null.");
                return;
            }

            // Check if we can randomize
            if (!CanRandomize())
                return;

            // Start randomization
            StartRandomization();

            // Call the avatar system randomize method
            if (avatarSystem != null)
            {
                avatarSystem.RandomizeRandomRow();

                if (showDebugMessages)
                    Debug.Log($"[RandomizeButtonHandler] {localPlayer.displayName} triggered avatar randomization.");
            }

            // Schedule cooldown reset
            SendCustomEventDelayedSeconds(nameof(ResetCooldown), cooldownTime);
        }

        private bool CanRandomize()
        {
            // Check if avatar system exists and is ready
            if (avatarSystem == null)
            {
                if (showDebugMessages)
                    Debug.LogWarning("[RandomizeButtonHandler] No avatar system assigned!");
                return false;
            }

            // Check cooldown
            if (Time.time - lastRandomizeTime < cooldownTime)
            {
                if (showDebugMessages)
                    Debug.Log("[RandomizeButtonHandler] Still on cooldown, please wait...");
                return false;
            }

            // Check if already loading
            if (isLoading)
            {
                if (showDebugMessages)
                    Debug.Log("[RandomizeButtonHandler] Already loading, please wait...");
                return false;
            }

            return true;
        }

        private void StartRandomization()
        {
            isLoading = true;
            lastRandomizeTime = Time.time;

            if (showDebugMessages)
                Debug.Log("[RandomizeButtonHandler] Starting randomization...");
        }

        public void ResetCooldown()
        {
            isLoading = false;

            if (showDebugMessages)
                Debug.Log("[RandomizeButtonHandler] Cooldown reset, ready for next interaction.");
        }

        // Public method to manually trigger randomization (for other scripts)
        public void TriggerRandomize()
        {
            Interact();
        }
    }
}
