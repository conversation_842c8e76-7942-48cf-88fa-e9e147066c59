using UdonSharp;
using UnityEngine;
using UnityEngine.UI;
using VRC.SDKBase;

namespace TheZiver
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class RandomizeButtonHandler : UdonSharpBehaviour
    {
        [Header("References")]
        [Toolt<PERSON>("The StringAvatarPedestal script to control")]
        public StringAvatarPedestal avatarSystem;
        
        [Toolt<PERSON>("The button that will trigger randomization")]
        public Button randomizeButton;
        
        [<PERSON><PERSON>("Button Settings")]
        [Tooltip("Text to show on button when ready")]
        public string buttonText = "🎲 Randomize";
        
        [Tooltip("Text to show on button when loading")]
        public string loadingText = "Loading...";
        
        [Tooltip("Disable button while loading to prevent spam")]
        public bool disableWhileLoading = true;
        
        [Tooltip("Cooldown time in seconds between randomizations")]
        public float cooldownTime = 1f;
        
        // Private variables
        private Text buttonTextComponent;
        private bool isLoading = false;
        private float lastRandomizeTime = 0f;
        
        void Start()
        {
            // Get the text component from the button
            if (randomizeButton != null)
            {
                buttonTextComponent = randomizeButton.GetComponentInChildren<Text>();
                if (buttonTextComponent != null)
                {
                    buttonTextComponent.text = buttonText;
                }
            }
            
            // Validate references
            if (avatarSystem == null)
            {
                Debug.LogError("RandomizeButtonHandler: Avatar System reference is missing!");
            }
            
            if (randomizeButton == null)
            {
                Debug.LogError("RandomizeButtonHandler: Randomize Button reference is missing!");
            }
        }
        
        public void OnButtonClick()
        {
            // Check if we can randomize
            if (!CanRandomize())
                return;
                
            // Start randomization
            StartRandomization();
            
            // Call the avatar system randomize method
            if (avatarSystem != null)
            {
                avatarSystem.RandomizeRandomRow();
            }
            
            // Schedule button re-enable
            if (disableWhileLoading)
            {
                SendCustomEventDelayedSeconds(nameof(EnableButton), cooldownTime);
            }
        }
        
        private bool CanRandomize()
        {
            // Check if avatar system exists and is ready
            if (avatarSystem == null)
            {
                Debug.LogWarning("RandomizeButtonHandler: No avatar system assigned!");
                return false;
            }
            
            // Check cooldown
            if (Time.time - lastRandomizeTime < cooldownTime)
            {
                Debug.Log("RandomizeButtonHandler: Still on cooldown, please wait...");
                return false;
            }
            
            // Check if already loading
            if (isLoading)
            {
                Debug.Log("RandomizeButtonHandler: Already loading, please wait...");
                return false;
            }
            
            return true;
        }
        
        private void StartRandomization()
        {
            isLoading = true;
            lastRandomizeTime = Time.time;
            
            // Update button appearance
            if (disableWhileLoading && randomizeButton != null)
            {
                randomizeButton.interactable = false;
            }
            
            if (buttonTextComponent != null)
            {
                buttonTextComponent.text = loadingText;
            }
        }
        
        public void EnableButton()
        {
            isLoading = false;
            
            // Re-enable button
            if (randomizeButton != null)
            {
                randomizeButton.interactable = true;
            }
            
            // Reset button text
            if (buttonTextComponent != null)
            {
                buttonTextComponent.text = buttonText;
            }
        }
        
        // Public method to manually trigger randomization (for other scripts)
        public void TriggerRandomize()
        {
            OnButtonClick();
        }
        
        // Public method to set custom button text
        public void SetButtonText(string newText)
        {
            buttonText = newText;
            if (buttonTextComponent != null && !isLoading)
            {
                buttonTextComponent.text = buttonText;
            }
        }
    }
}
