#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using TMPro;

public class DateDisplaySetup : MonoBehaviour
{
    [MenuItem("Tools/Avatar Pedestal/Create Date Display Prefab")]
    public static void CreateDateDisplayPrefab()
    {
        // Create the main date display GameObject
        GameObject dateDisplayObj = new GameObject("DateDisplayPrefab");
        
        // Add Canvas for UI text
        Canvas canvas = dateDisplayObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = null; // Will use main camera
        
        // Add CanvasScaler for consistent sizing
        CanvasScaler scaler = dateDisplayObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPixelSize;
        scaler.scaleFactor = 1f;
        
        // Add GraphicRaycaster (required for Canvas)
        dateDisplayObj.AddComponent<GraphicRaycaster>();
        
        // Set canvas size
        RectTransform canvasRect = dateDisplayObj.GetComponent<RectTransform>();
        canvasRect.sizeDelta = new Vector2(200, 50);
        
        // Create text object
        GameObject textObj = new GameObject("DateText");
        textObj.transform.SetParent(dateDisplayObj.transform);
        
        // Add TextMeshProUGUI component
        TextMeshProUGUI textComponent = textObj.AddComponent<TextMeshProUGUI>();
        textComponent.text = "Jun 25, 2025";
        textComponent.fontSize = 14;
        textComponent.color = Color.white;
        textComponent.alignment = TextAlignmentOptions.Center;
        textComponent.fontStyle = FontStyles.Bold;
        
        // Set text rect transform
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.sizeDelta = Vector2.zero;
        textRect.anchoredPosition = Vector2.zero;
        
        // Add background panel for better visibility
        GameObject backgroundObj = new GameObject("Background");
        backgroundObj.transform.SetParent(dateDisplayObj.transform);
        backgroundObj.transform.SetAsFirstSibling(); // Put behind text
        
        UnityEngine.UI.Image backgroundImage = backgroundObj.AddComponent<UnityEngine.UI.Image>();
        backgroundImage.color = new Color(0, 0, 0, 0.7f); // Semi-transparent black
        
        RectTransform backgroundRect = backgroundObj.GetComponent<RectTransform>();
        backgroundRect.anchorMin = Vector2.zero;
        backgroundRect.anchorMax = Vector2.one;
        backgroundRect.sizeDelta = Vector2.zero;
        backgroundRect.anchoredPosition = Vector2.zero;
        
        // Position the canvas to face forward
        dateDisplayObj.transform.rotation = Quaternion.identity;
        
        // Save as prefab
        string prefabPath = "Assets/StringAvatarPedestal/DateDisplayPrefab.prefab";
        PrefabUtility.SaveAsPrefabAsset(dateDisplayObj, prefabPath);
        
        // Clean up the scene object
        DestroyImmediate(dateDisplayObj);
        
        Debug.Log($"Date Display Prefab created at: {prefabPath}");
        Debug.Log("Assign this prefab to the 'Date Prefab' field in your StringAvatarPedestal component.");
        
        // Select the created prefab in the project window
        Object prefabAsset = AssetDatabase.LoadAssetAtPath<Object>(prefabPath);
        Selection.activeObject = prefabAsset;
        EditorGUIUtility.PingObject(prefabAsset);
    }
    
    [MenuItem("Tools/Avatar Pedestal/Create 3D Date Display Prefab")]
    public static void Create3DDateDisplayPrefab()
    {
        // Create the main date display GameObject
        GameObject dateDisplayObj = new GameObject("DateDisplay3DPrefab");
        
        // Add TextMeshPro (3D) component
        TextMeshPro textComponent = dateDisplayObj.AddComponent<TextMeshPro>();
        textComponent.text = "Jun 25, 2025";
        textComponent.fontSize = 2;
        textComponent.color = Color.white;
        textComponent.alignment = TextAlignmentOptions.Center;
        textComponent.fontStyle = FontStyles.Bold;
        
        // Set the rect transform for proper sizing
        RectTransform rectTransform = dateDisplayObj.GetComponent<RectTransform>();
        rectTransform.sizeDelta = new Vector2(4, 1);
        
        // Position to face forward
        dateDisplayObj.transform.rotation = Quaternion.identity;
        
        // Save as prefab
        string prefabPath = "Assets/StringAvatarPedestal/DateDisplay3DPrefab.prefab";
        PrefabUtility.SaveAsPrefabAsset(dateDisplayObj, prefabPath);
        
        // Clean up the scene object
        DestroyImmediate(dateDisplayObj);
        
        Debug.Log($"3D Date Display Prefab created at: {prefabPath}");
        Debug.Log("Assign this prefab to the 'Date Prefab' field in your StringAvatarPedestal component.");
        
        // Select the created prefab in the project window
        Object prefabAsset = AssetDatabase.LoadAssetAtPath<Object>(prefabPath);
        Selection.activeObject = prefabAsset;
        EditorGUIUtility.PingObject(prefabAsset);
    }
}
#endif
