using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using VRC.SDK3.StringLoading;
using VRC.Udon.Common.Interfaces;
using System.Collections;
using VRC.SDK3.Components;
using UnityEngine.UI;



public enum PlacementDirection
{
    PositiveX,
    NegativeX,
    PositiveZ,
    NegativeZ
}

[UdonBehaviourSyncMode(BehaviourSyncMode.None)]
public class StringAvatarPedestal : UdonSharpBehaviour
{
    [Header("Main Row Configuration")]
    public GameObject avatarPedestalPrefab;
    public PlacementDirection placementDirection = PlacementDirection.PositiveX;
    public float pedestalSpacing = 2f;
    public int stackHeight = 1; // How many pedestals to stack vertically
    public float stackSpacing = 2.5f; // Vertical spacing between stacked pedestals
    public float pedestalScale = 1f; // Scale of the avatar pedestals

    [Header("Tag-Based Prefab System")]
    [Tooltip("Tag names to look for (e.g., 'FISH', 'ROSE_FISH', 'VAPOR_FISH')")]
    public string[] tagNames;

    [Tooltip("Prefabs to spawn for each tag (must match tagNames array order)")]
    public GameObject[] tagPrefabs;

    [Tooltip("Priority for each tag (higher = higher priority, must match tagNames array order)")]
    public int[] tagPriorities;

    [Tooltip("Offset position for each tag prefab (must match tagNames array order)")]
    public Vector3[] tagOffsets;

    [Tooltip("Scale for each tag prefab (must match tagNames array order)")]
    public float[] tagScales;

    [Header("Random Row Configuration")]
    public bool enableRandomRow = true;
    public int randomRowCount = 25; // Number of random avatars to display
    public PlacementDirection randomRowDirection = PlacementDirection.PositiveZ;
    public float randomRowSpacing = 2f;
    public int randomRowStackHeight = 1; // How many pedestals to stack vertically in random row
    public float randomRowStackSpacing = 2.5f; // Vertical spacing between stacked pedestals in random row
    public float randomRowScale = 1f; // Scale of the random row pedestals



    [Header("Performance Settings")]
    public int chunkSize = 5;
    public float chunkDelay = 0.1f;

    [Header("Data Source")]
    [SerializeField]
    private VRCUrl mainUrl = new VRCUrl("https://gist.githubusercontent.com/TheZiver/bb99f9facb8d14fd607dbb79e9a99d83/raw");

    [Header("Display Options")]
    public Transform pedestalParent; // Parent for main row pedestals
    public Transform randomRowParent; // Parent for random row pedestals
    public Transform tagPrefabParent; // Optional parent for tag prefabs (null = no parent)
    public TextMeshProUGUI avatarCountText; // Display for avatar count

    [Header("Randomize Controls")]
    [Tooltip("Call the 'RandomizeRandomRow' method from a UI button or other trigger")]
    public bool enableRandomizeButton = true; // Enable randomize functionality

    // Runtime variables
    private VRCAvatarPedestal[] avatarPedestals; // Main row pedestals
    private VRCAvatarPedestal[] randomRowPedestals; // Random row pedestals
    private GameObject[] mainRowTagPrefabs; // Tag prefabs for main row
    private GameObject[] randomRowTagPrefabs; // Tag prefabs for random row
    private string[] avatarData; // Store parsed avatar data
    private bool[] isRoseFishAvatar; // Track which avatars have ROSE_FISH tag (legacy)
    private int[] avatarHighestTagIndex; // Store highest priority tag index for each avatar (-1 = no tag)
    private int[] sortedIndices; // Avatar indices sorted by priority (ROSE_FISH first)
    private int[] randomIndices; // Store random avatar indices for random row
    private bool downloaded = false;
    private bool pedestalsCreated = false;
    private bool randomRowCreated = false;
    private int totalAvatars = 0;
    private int roseFishCount = 0;
    private int currentChunk = 0;
    private int randomRowChunk = 0;

    void Start()
    {
        LoadAvatarData();
    }

    private void LoadAvatarData()
    {
        if (!downloaded)
        {
            VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
            downloaded = true;
        }
    }

    private void CreateAvatarPedestals()
    {
        if (pedestalsCreated || avatarPedestalPrefab == null || totalAvatars == 0)
            return;

        // Initialize arrays based on actual avatar count
        avatarPedestals = new VRCAvatarPedestal[totalAvatars];
        mainRowTagPrefabs = new GameObject[totalAvatars];
        avatarData = new string[totalAvatars];
        isRoseFishAvatar = new bool[totalAvatars];
        avatarHighestTagIndex = new int[totalAvatars];
        sortedIndices = new int[totalAvatars];

        // Initialize tag indices to -1 (no tag)
        for (int i = 0; i < totalAvatars; i++)
        {
            avatarHighestTagIndex[i] = -1;
        }

        // Set parent if not specified
        if (pedestalParent == null)
            pedestalParent = this.transform;

        // Create pedestals with stacking (will be populated later with correct prefabs)
        // Note: Pedestals will be created during loading phase when we know which are ROSE_FISH

        // Update avatar count display
        UpdateAvatarCountDisplay();

        pedestalsCreated = true;
    }

    private void CreateRandomRowPedestals()
    {
        if (randomRowCreated || avatarPedestalPrefab == null || !enableRandomRow)
            return;

        // Limit random row count to available avatars
        int actualRandomCount = Mathf.Min(randomRowCount, totalAvatars);

        // Initialize random row arrays
        randomRowPedestals = new VRCAvatarPedestal[actualRandomCount];
        randomRowTagPrefabs = new GameObject[actualRandomCount];

        // Set random row parent if not specified, fallback to main parent or this transform
        Transform randomParent = randomRowParent;
        if (randomParent == null)
            randomParent = pedestalParent != null ? pedestalParent : this.transform;

        // Random row pedestals will be created during loading phase

        randomRowCreated = true;
    }

    private Vector3 CalculateRandomRowPosition(int index, Transform parent)
    {
        Vector3 basePosition = parent.position;

        // Calculate row and stack position for linear random row
        int rowIndex = index / randomRowStackHeight; // Which row this pedestal is in
        int stackIndex = index % randomRowStackHeight; // Which level in the stack

        Vector3 rowOffset = Vector3.zero;
        Vector3 stackOffset = new Vector3(0, stackIndex * randomRowStackSpacing, 0);

        switch (randomRowDirection)
        {
            case PlacementDirection.PositiveX:
                rowOffset = new Vector3(rowIndex * randomRowSpacing, 0, 0);
                break;

            case PlacementDirection.NegativeX:
                rowOffset = new Vector3(-rowIndex * randomRowSpacing, 0, 0);
                break;

            case PlacementDirection.PositiveZ:
                rowOffset = new Vector3(0, 0, rowIndex * randomRowSpacing);
                break;

            case PlacementDirection.NegativeZ:
                rowOffset = new Vector3(0, 0, -rowIndex * randomRowSpacing);
                break;

            default:
                rowOffset = new Vector3(rowIndex * randomRowSpacing, 0, 0);
                break;
        }

        return basePosition + rowOffset + stackOffset;
    }

    private Vector3 CalculatePedestalPosition(int index)
    {
        Vector3 basePosition = pedestalParent.position;

        // Calculate row and stack position
        int rowIndex = index / stackHeight; // Which row this pedestal is in
        int stackIndex = index % stackHeight; // Which level in the stack

        Vector3 rowOffset = Vector3.zero;
        Vector3 stackOffset = new Vector3(0, stackIndex * stackSpacing, 0);

        switch (placementDirection)
        {
            case PlacementDirection.PositiveX:
                rowOffset = new Vector3(rowIndex * pedestalSpacing, 0, 0);
                break;

            case PlacementDirection.NegativeX:
                rowOffset = new Vector3(-rowIndex * pedestalSpacing, 0, 0);
                break;

            case PlacementDirection.PositiveZ:
                rowOffset = new Vector3(0, 0, rowIndex * pedestalSpacing);
                break;

            case PlacementDirection.NegativeZ:
                rowOffset = new Vector3(0, 0, -rowIndex * pedestalSpacing);
                break;

            default:
                rowOffset = new Vector3(rowIndex * pedestalSpacing, 0, 0);
                break;
        }

        return basePosition + rowOffset + stackOffset;
    }

    private void UpdateAvatarCountDisplay()
    {
        if (avatarCountText != null)
        {
            string displayText = $"Avatars: {totalAvatars}";

            if (roseFishCount > 0)
            {
                displayText += $" | ROSE_FISH: {roseFishCount}";
            }

            if (enableRandomRow && randomRowPedestals != null)
            {
                displayText += $" | Random: {randomRowPedestals.Length}";
            }

            avatarCountText.text = displayText;
        }
    }

    private int CountAvatarsInJson(string jsonData)
    {
        // Clean up the JSON data
        string cleanedJson = jsonData.Replace("\\_", "_").Replace("\\\"", "\"");

        // Find community_avatars array
        string[] searchPatterns = {
            "\"community_avatars\":[",
            "\"community_avatars\": [",
            "community_avatars\":[",
            "community_avatars\": ["
        };

        int startIndex = -1;
        string foundPattern = "";

        foreach (string pattern in searchPatterns)
        {
            startIndex = cleanedJson.IndexOf(pattern);
            if (startIndex != -1)
            {
                foundPattern = pattern;
                break;
            }
        }

        if (startIndex == -1) return 0;

        // Count objects in the array
        startIndex += foundPattern.Length;
        int count = 0;
        int currentPos = startIndex;

        while (currentPos < cleanedJson.Length)
        {
            int objectStart = cleanedJson.IndexOf('{', currentPos);
            if (objectStart == -1) break;

            // Find the end of this object
            int bracketCount = 1;
            int objectEnd = objectStart + 1;
            for (int i = objectStart + 1; i < cleanedJson.Length && bracketCount > 0; i++)
            {
                if (cleanedJson[i] == '{') bracketCount++;
                else if (cleanedJson[i] == '}') bracketCount--;
                objectEnd = i;
            }

            if (bracketCount != 0) break;

            count++;
            currentPos = objectEnd + 1;

            // Check if we've reached the end of the array
            int nextComma = cleanedJson.IndexOf(',', currentPos);
            int arrayEnd = cleanedJson.IndexOf(']', currentPos);
            if (arrayEnd != -1 && (nextComma == -1 || arrayEnd < nextComma))
                break;
        }

        return count;
    }

    private void GenerateRandomIndices()
    {
        if (!enableRandomRow || totalAvatars == 0)
            return;

        int actualRandomCount = Mathf.Min(randomRowCount, totalAvatars);
        randomIndices = new int[actualRandomCount];

        // Create a simple random selection without duplicates
        bool[] used = new bool[totalAvatars];

        for (int i = 0; i < actualRandomCount; i++)
        {
            int randomIndex;
            int attempts = 0;

            // Try to find an unused index (with fallback to prevent infinite loop)
            do
            {
                randomIndex = Random.Range(0, totalAvatars);
                attempts++;
            } while (used[randomIndex] && attempts < 100);

            used[randomIndex] = true;
            randomIndices[i] = randomIndex;
        }
    }

    public void LoadRandomRowChunk()
    {
        if (!enableRandomRow || randomRowPedestals == null || randomIndices == null ||
            randomRowChunk * chunkSize >= randomRowPedestals.Length)
        {
            Debug.Log("[StringAvatarPedestal] LoadRandomRowChunk: Cannot load - conditions not met");
            return;
        }

        int startIndex = randomRowChunk * chunkSize;
        int endIndex = Mathf.Min(startIndex + chunkSize, randomRowPedestals.Length);

        Debug.Log($"[StringAvatarPedestal] Loading random chunk {randomRowChunk}: indices {startIndex} to {endIndex-1}");

        for (int i = startIndex; i < endIndex; i++)
        {
            int avatarDataIndex = randomIndices[i];
            if (avatarDataIndex < avatarData.Length && !string.IsNullOrEmpty(avatarData[avatarDataIndex]))
            {
                // Create random pedestal if it doesn't exist yet
                if (randomRowPedestals[i] == null)
                {
                    CreateSingleRandomPedestal(i, avatarDataIndex);
                    Debug.Log($"[StringAvatarPedestal] Created random pedestal {i} for avatar {avatarDataIndex}");
                }

                LoadSingleRandomAvatar(i, avatarData[avatarDataIndex]);
            }
        }

        randomRowChunk++;

        // Schedule next chunk if there are more avatars
        if (randomRowChunk * chunkSize < randomRowPedestals.Length)
        {
            SendCustomEventDelayedSeconds(nameof(LoadRandomRowChunk), chunkDelay);
        }
        else
        {
            Debug.Log("[StringAvatarPedestal] Random row loading complete!");
        }
    }

    private void LoadSingleRandomAvatar(int pedestalIndex, string avatarObject)
    {
        if (randomRowPedestals == null || pedestalIndex >= randomRowPedestals.Length ||
            randomRowPedestals[pedestalIndex] == null)
            return;

        // Parse the avatar data
        string avatarId = ExtractJsonValue(avatarObject, "avatar_id");

        // Assign to random row pedestal
        if (!string.IsNullOrEmpty(avatarId))
        {
            randomRowPedestals[pedestalIndex].blueprintId = avatarId;

            // Create tag prefab if avatar has a tag
            int originalIndex = randomIndices[pedestalIndex];
            if (avatarHighestTagIndex[originalIndex] >= 0)
            {
                CreateTagPrefab(randomRowPedestals[pedestalIndex].transform, avatarHighestTagIndex[originalIndex], ref randomRowTagPrefabs[pedestalIndex]);
            }
        }
    }

    public override void OnStringLoadSuccess(IVRCStringDownload result)
    {
        if (result.Result != null)
        {
            // First count how many avatars we have
            totalAvatars = CountAvatarsInJson(result.Result);

            if (totalAvatars > 0)
            {
                // Create main row pedestals
                if (!pedestalsCreated)
                {
                    CreateAvatarPedestals();
                }

                // Create random row pedestals
                if (enableRandomRow && !randomRowCreated)
                {
                    CreateRandomRowPedestals();
                }

                // Parse and store avatar data
                ParseJsonAndStoreAvatars(result.Result);

                // Generate random indices for random row
                if (enableRandomRow)
                {
                    GenerateRandomIndices();
                }

                // Start chunked loading for main row
                currentChunk = 0;
                SendCustomEventDelayedSeconds(nameof(LoadNextChunk), chunkDelay);

                // Start chunked loading for random row
                if (enableRandomRow)
                {
                    randomRowChunk = 0;
                    SendCustomEventDelayedSeconds(nameof(LoadRandomRowChunk), chunkDelay * 2);
                }
            }
        }

        // Local-only mode: No auto-reload needed
    }

    public override void OnStringLoadError(IVRCStringDownload result)
    {
        Debug.LogError($"Failed to load JSON data: {result.Error}");
        Debug.LogError($"Error code: {result.ErrorCode}");
    }

    private void ParseJsonAndStoreAvatars(string jsonData)
    {
        if (avatarData == null || avatarData.Length == 0)
            return;

        // Clean up the JSON data
        string cleanedJson = jsonData.Replace("\\_", "_").Replace("\\\"", "\"");

        // Find community_avatars array
        string[] searchPatterns = {
            "\"community_avatars\":[",
            "\"community_avatars\": [",
            "community_avatars\":[",
            "community_avatars\": ["
        };

        int startIndex = -1;
        string foundPattern = "";

        foreach (string pattern in searchPatterns)
        {
            startIndex = cleanedJson.IndexOf(pattern);
            if (startIndex != -1)
            {
                foundPattern = pattern;
                break;
            }
        }

        if (startIndex == -1) return;

        // Move to the start of the array content
        startIndex += foundPattern.Length;

        // Parse and store avatar data
        int avatarIndex = 0;
        int currentPos = startIndex;
        roseFishCount = 0;

        while (currentPos < cleanedJson.Length && avatarIndex < avatarData.Length)
        {
            // Find the start of the next avatar object
            int objectStart = cleanedJson.IndexOf('{', currentPos);
            if (objectStart == -1) break;

            // Find the end of this avatar object
            int bracketCount = 1;
            int objectEnd = objectStart + 1;
            for (int i = objectStart + 1; i < cleanedJson.Length && bracketCount > 0; i++)
            {
                if (cleanedJson[i] == '{') bracketCount++;
                else if (cleanedJson[i] == '}') bracketCount--;
                objectEnd = i;
            }

            if (bracketCount != 0) break;

            // Store the avatar object data
            string avatarObject = cleanedJson.Substring(objectStart, objectEnd - objectStart + 1);
            avatarData[avatarIndex] = avatarObject;

            // Check for ROSE_FISH tag in tags array (legacy support)
            isRoseFishAvatar[avatarIndex] = HasRoseFishTag(avatarObject);
            if (isRoseFishAvatar[avatarIndex])
            {
                roseFishCount++;
            }

            // Find highest priority tag for this avatar
            avatarHighestTagIndex[avatarIndex] = GetHighestPriorityTagIndex(avatarObject);

            avatarIndex++;
            currentPos = objectEnd + 1;
        }

        // Sort avatars with ROSE_FISH first
        SortAvatarsByPriority();
    }

    private void SortAvatarsByPriority()
    {
        // Create sorted indices array with ROSE_FISH avatars first
        int roseFishIndex = 0;
        int regularIndex = roseFishCount;

        for (int i = 0; i < totalAvatars; i++)
        {
            if (isRoseFishAvatar[i])
            {
                sortedIndices[roseFishIndex] = i;
                roseFishIndex++;
            }
            else
            {
                sortedIndices[regularIndex] = i;
                regularIndex++;
            }
        }
    }

    public void LoadNextChunk()
    {
        if (avatarData == null || sortedIndices == null || currentChunk * chunkSize >= totalAvatars)
            return;

        int startIndex = currentChunk * chunkSize;
        int endIndex = Mathf.Min(startIndex + chunkSize, totalAvatars);

        for (int i = startIndex; i < endIndex; i++)
        {
            int originalIndex = sortedIndices[i]; // Get original avatar index in priority order
            if (!string.IsNullOrEmpty(avatarData[originalIndex]))
            {
                // Create pedestal if it doesn't exist yet
                if (avatarPedestals[i] == null)
                {
                    CreateSinglePedestal(i, originalIndex);
                }

                // Load avatar data
                LoadSingleAvatar(i, avatarData[originalIndex]);
            }
        }

        currentChunk++;

        // Schedule next chunk if there are more avatars
        if (currentChunk * chunkSize < totalAvatars)
        {
            SendCustomEventDelayedSeconds(nameof(LoadNextChunk), chunkDelay);
        }
    }

    private void CreateSinglePedestal(int pedestalIndex, int originalAvatarIndex)
    {
        if (avatarPedestalPrefab == null)
            return;

        // Set parent if not specified
        if (pedestalParent == null)
            pedestalParent = this.transform;

        Vector3 position = CalculatePedestalPosition(pedestalIndex);

        // Instantiate pedestal (always use standard prefab, tags handle differentiation)
        GameObject pedestalObj = Instantiate(avatarPedestalPrefab, position, pedestalParent.rotation);
        pedestalObj.transform.SetParent(pedestalParent);
        pedestalObj.name = $"AvatarPedestal_{pedestalIndex}";

        // Apply scale
        pedestalObj.transform.localScale = Vector3.one * pedestalScale;

        // Get the VRCAvatarPedestal component
        avatarPedestals[pedestalIndex] = pedestalObj.GetComponent<VRCAvatarPedestal>();
    }

    private void CreateSingleRandomPedestal(int pedestalIndex, int originalAvatarIndex)
    {
        if (avatarPedestalPrefab == null)
            return;

        // Set random row parent if not specified, fallback to main parent or this transform
        Transform randomParent = randomRowParent;
        if (randomParent == null)
            randomParent = pedestalParent != null ? pedestalParent : this.transform;

        Vector3 position = CalculateRandomRowPosition(pedestalIndex, randomParent);

        // Use parent rotation for all random row pedestals
        Quaternion rotation = randomParent.rotation;

        // Instantiate pedestal (always use standard prefab, tags handle differentiation)
        GameObject pedestalObj = Instantiate(avatarPedestalPrefab, position, rotation);
        pedestalObj.transform.SetParent(randomParent);
        pedestalObj.name = $"RandomPedestal_{pedestalIndex}";

        // Apply random row scale
        pedestalObj.transform.localScale = Vector3.one * randomRowScale;

        // Get the VRCAvatarPedestal component
        randomRowPedestals[pedestalIndex] = pedestalObj.GetComponent<VRCAvatarPedestal>();
    }

    private void LoadSingleAvatar(int index, string avatarObject)
    {
        if (avatarPedestals == null || index >= avatarPedestals.Length || avatarPedestals[index] == null)
            return;

        // Parse the avatar data
        string avatarId = ExtractJsonValue(avatarObject, "avatar_id");

        // Assign to pedestal
        if (!string.IsNullOrEmpty(avatarId))
        {
            avatarPedestals[index].blueprintId = avatarId;

            // Create tag prefab if avatar has a tag
            int originalIndex = sortedIndices[index];
            if (avatarHighestTagIndex[originalIndex] >= 0)
            {
                CreateTagPrefab(avatarPedestals[index].transform, avatarHighestTagIndex[originalIndex], ref mainRowTagPrefabs[index]);
            }
        }
    }



    private string ExtractJsonValue(string json, string key)
    {
        // Try multiple patterns for the key
        string[] patterns = {
            $"\"{key}\":\"",
            $"\"{key}\": \"",
            $"\"{key}\" :\"",
            $"\"{key}\" : \"",
            $"{key}\":\"",
            $"{key}\": \""
        };

        foreach (string pattern in patterns)
        {
            int startIndex = json.IndexOf(pattern);
            if (startIndex != -1)
            {
                startIndex += pattern.Length;
                int endIndex = json.IndexOf('"', startIndex);
                if (endIndex != -1)
                {
                    string value = json.Substring(startIndex, endIndex - startIndex);
                    // Clean up escaped characters
                    value = value.Replace("\\_", "_").Replace("\\\"", "\"");
                    return value;
                }
            }
        }

        return "";
    }

    private bool HasRoseFishTag(string avatarObject)
    {
        // Find the tags array in the avatar object
        string tagsPattern = "\"tags\":[";
        int tagsStart = avatarObject.IndexOf(tagsPattern);
        if (tagsStart == -1)
        {
            // Try alternative pattern with space
            tagsPattern = "\"tags\": [";
            tagsStart = avatarObject.IndexOf(tagsPattern);
            if (tagsStart == -1) return false;
        }

        // Move to start of array content
        tagsStart += tagsPattern.Length;

        // Find the end of the tags array
        int tagsEnd = avatarObject.IndexOf(']', tagsStart);
        if (tagsEnd == -1) return false;

        // Extract the tags array content
        string tagsContent = avatarObject.Substring(tagsStart, tagsEnd - tagsStart);

        // Check if ROSE_FISH is in the tags array
        return tagsContent.Contains("\"ROSE_FISH\"");
    }

    private int GetHighestPriorityTagIndex(string avatarObject)
    {
        if (tagNames == null || tagNames.Length == 0 || tagPrefabs == null || tagPriorities == null)
            return -1;

        // Find the tags array in the avatar object
        string tagsPattern = "\"tags\":[";
        int tagsStart = avatarObject.IndexOf(tagsPattern);
        if (tagsStart == -1)
        {
            tagsPattern = "\"tags\": [";
            tagsStart = avatarObject.IndexOf(tagsPattern);
            if (tagsStart == -1) return -1;
        }

        // Move to start of array content
        tagsStart += tagsPattern.Length;

        // Find the end of the tags array
        int tagsEnd = avatarObject.IndexOf(']', tagsStart);
        if (tagsEnd == -1) return -1;

        // Extract the tags array content
        string tagsContent = avatarObject.Substring(tagsStart, tagsEnd - tagsStart);

        // Find the highest priority tag that exists in this avatar
        int highestPriorityIndex = -1;
        int highestPriority = -1;

        if (tagNames == null || tagPrefabs == null || tagPriorities == null)
            return -1;

        for (int i = 0; i < tagNames.Length; i++)
        {
            if (i < tagPrefabs.Length && i < tagPriorities.Length &&
                !string.IsNullOrEmpty(tagNames[i]) && tagPrefabs[i] != null)
            {
                string searchTag = $"\"{tagNames[i]}\"";
                if (tagsContent.Contains(searchTag) && tagPriorities[i] > highestPriority)
                {
                    highestPriority = tagPriorities[i];
                    highestPriorityIndex = i;
                }
            }
        }

        return highestPriorityIndex;
    }
    private void CreateTagPrefab(Transform pedestalTransform, int tagIndex, ref GameObject tagPrefabInstance)
    {
        if (tagPrefabs == null || tagIndex < 0 || tagIndex >= tagPrefabs.Length || tagPrefabs[tagIndex] == null || pedestalTransform == null)
            return;

        // Destroy existing tag prefab if it exists
        if (tagPrefabInstance != null)
        {
            Destroy(tagPrefabInstance);
        }

        // Get offset and scale (with defaults if arrays are too short)
        Vector3 offset = (tagOffsets != null && tagIndex < tagOffsets.Length) ? tagOffsets[tagIndex] : new Vector3(0, 2f, 0);
        float scale = (tagScales != null && tagIndex < tagScales.Length) ? tagScales[tagIndex] : 1f;

        // Calculate world position with offset
        Vector3 spawnPosition = pedestalTransform.position + offset;

        // Instantiate the tag prefab as independent object (not parented to pedestal)
        tagPrefabInstance = Instantiate(tagPrefabs[tagIndex], spawnPosition, pedestalTransform.rotation);

        // Optionally parent to tag prefab parent for organization
        if (tagPrefabParent != null)
        {
            tagPrefabInstance.transform.SetParent(tagPrefabParent);
        }

        // Set name (with fallback if tagNames array is too short)
        string tagName = (tagNames != null && tagIndex < tagNames.Length) ? tagNames[tagIndex] : $"Tag{tagIndex}";
        tagPrefabInstance.name = $"TagPrefab_{tagName}";

        // Apply scale
        tagPrefabInstance.transform.localScale = Vector3.one * scale;
    }





    public void RandomizeRandomRow()
    {
        if (!enableRandomizeButton || !enableRandomRow || avatarData == null || totalAvatars == 0)
            return;

        // Clear existing random row pedestals
        ClearRandomRowPedestals();

        // Generate new random indices
        GenerateRandomIndices();

        // Recreate random row pedestals and tag prefab arrays
        int actualRandomCount = Mathf.Min(randomRowCount, totalAvatars);
        randomRowPedestals = new VRCAvatarPedestal[actualRandomCount];
        randomRowTagPrefabs = new GameObject[actualRandomCount];

        // Start loading the new random selection immediately
        randomRowChunk = 0;
        LoadRandomRowChunk();
    }

    private void ClearRandomRowPedestals()
    {
        if (randomRowPedestals == null) return;

        // Destroy existing random row pedestals and tag prefabs
        for (int i = 0; i < randomRowPedestals.Length; i++)
        {
            // Destroy tag prefab first
            if (randomRowTagPrefabs != null && i < randomRowTagPrefabs.Length && randomRowTagPrefabs[i] != null)
            {
                Destroy(randomRowTagPrefabs[i]);
                randomRowTagPrefabs[i] = null;
            }

            // Destroy pedestal
            if (randomRowPedestals[i] != null && randomRowPedestals[i].gameObject != null)
            {
                Destroy(randomRowPedestals[i].gameObject);
            }
        }

        randomRowPedestals = null;
        randomRowTagPrefabs = null;
    }
}
