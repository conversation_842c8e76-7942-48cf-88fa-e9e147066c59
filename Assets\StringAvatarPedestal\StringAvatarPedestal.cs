using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using VRC.SDK3.StringLoading;
using VRC.Udon.Common.Interfaces;
using System.Collections;
using VRC.SDK3.Components;

public class StringAvatarPedestal : UdonSharpBehaviour
{
    [Header("Pedestal Configuration")]
    public GameObject avatarPedestalPrefab;
    public int maxAvatars = 50;
    public float pedestalSpacing = 2f;
    public bool showAvatarInfo = true;

    [Header("Update Settings")]
    public float updateInterval = 30f;

    [Header("Data Source")]
    [SerializeField]
    private VRCUrl mainUrl;

    [Header("Display Options")]
    public GameObject avatarInfoPrefab; // Optional prefab for displaying avatar name/author
    public Transform pedestalParent; // Parent object for organizing pedestals

    // Runtime variables
    private VRCAvatarPedestal[] avatarPedestals;
    private GameObject[] avatarInfoDisplays;
    private bool downloaded = false;
    private bool isWaiting = false;
    private bool pedestalsCreated = false;

    void Start()
    {
        CreateAvatarPedestals();
    }

    public override void OnPlayerJoined(VRCPlayerApi player)
    {
        if (!downloaded)
        {
            VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
            downloaded = true;
        }
    }

    private void CreateAvatarPedestals()
    {
        if (pedestalsCreated || avatarPedestalPrefab == null)
            return;

        // Initialize arrays
        avatarPedestals = new VRCAvatarPedestal[maxAvatars];
        if (showAvatarInfo && avatarInfoPrefab != null)
        {
            avatarInfoDisplays = new GameObject[maxAvatars];
        }

        // Set parent if not specified
        if (pedestalParent == null)
            pedestalParent = this.transform;

        // Create pedestals in a row
        for (int i = 0; i < maxAvatars; i++)
        {
            // Calculate position
            Vector3 position = pedestalParent.position + new Vector3(i * pedestalSpacing, 0, 0);

            // Instantiate pedestal
            GameObject pedestalObj = Instantiate(avatarPedestalPrefab, position, pedestalParent.rotation);
            pedestalObj.transform.SetParent(pedestalParent);
            pedestalObj.name = $"AvatarPedestal_{i}";

            // Get the VRCAvatarPedestal component
            avatarPedestals[i] = pedestalObj.GetComponent<VRCAvatarPedestal>();

            // Create info display if enabled
            if (showAvatarInfo && avatarInfoPrefab != null)
            {
                Vector3 infoPosition = position + new Vector3(0, 2f, 0); // Above the pedestal
                GameObject infoObj = Instantiate(avatarInfoPrefab, infoPosition, pedestalParent.rotation);
                infoObj.transform.SetParent(pedestalParent);
                infoObj.name = $"AvatarInfo_{i}";
                avatarInfoDisplays[i] = infoObj;
            }
        }

        pedestalsCreated = true;
    }

    public override void OnStringLoadSuccess(IVRCStringDownload result)
    {
        if (!pedestalsCreated)
        {
            CreateAvatarPedestals();
        }

        ParseJsonAndAssignAvatars(result.Result);

        if (!isWaiting)
        {
            isWaiting = true;
            SendCustomEventDelayedSeconds(nameof(Reload), updateInterval);
        }
    }

    private void ParseJsonAndAssignAvatars(string jsonData)
    {
        if (avatarPedestals == null || avatarPedestals.Length == 0)
            return;

        // Find the community_avatars array in the JSON
        string searchPattern = "\"community_avatars\":[";
        int startIndex = jsonData.IndexOf(searchPattern);
        if (startIndex == -1)
        {
            Debug.LogError("Could not find community_avatars in JSON data");
            return;
        }

        // Move to the start of the array content
        startIndex += searchPattern.Length;

        // Find the end of the array
        int bracketCount = 1;
        int endIndex = startIndex;
        for (int i = startIndex; i < jsonData.Length && bracketCount > 0; i++)
        {
            if (jsonData[i] == '[') bracketCount++;
            else if (jsonData[i] == ']') bracketCount--;
            endIndex = i;
        }

        if (bracketCount != 0)
        {
            Debug.LogError("Malformed JSON: Could not find end of community_avatars array");
            return;
        }

        // Extract the array content
        string arrayContent = jsonData.Substring(startIndex, endIndex - startIndex);

        // Parse individual avatar objects
        ParseAvatarObjects(arrayContent);
    }

    private void ParseAvatarObjects(string arrayContent)
    {
        int avatarIndex = 0;
        int currentPos = 0;

        while (currentPos < arrayContent.Length && avatarIndex < avatarPedestals.Length)
        {
            // Find the start of the next avatar object
            int objectStart = arrayContent.IndexOf('{', currentPos);
            if (objectStart == -1) break;

            // Find the end of this avatar object
            int bracketCount = 1;
            int objectEnd = objectStart + 1;
            for (int i = objectStart + 1; i < arrayContent.Length && bracketCount > 0; i++)
            {
                if (arrayContent[i] == '{') bracketCount++;
                else if (arrayContent[i] == '}') bracketCount--;
                objectEnd = i;
            }

            if (bracketCount != 0) break; // Malformed object

            // Extract the avatar object
            string avatarObject = arrayContent.Substring(objectStart, objectEnd - objectStart + 1);

            // Parse the avatar data
            string avatarId = ExtractJsonValue(avatarObject, "avatar_id");
            string avatarName = ExtractJsonValue(avatarObject, "avatar_name");
            string author = ExtractJsonValue(avatarObject, "author");

            // Assign to pedestal
            if (!string.IsNullOrEmpty(avatarId) && avatarPedestals[avatarIndex] != null)
            {
                avatarPedestals[avatarIndex].blueprintId = avatarId;

                // Update info display if available
                if (showAvatarInfo && avatarInfoDisplays != null && avatarInfoDisplays[avatarIndex] != null)
                {
                    UpdateAvatarInfoDisplay(avatarIndex, avatarName, author);
                }
            }

            avatarIndex++;
            currentPos = objectEnd + 1;
        }

        Debug.Log($"Loaded {avatarIndex} avatars from JSON data");
    }

    private string ExtractJsonValue(string json, string key)
    {
        string searchPattern = $"\"{key}\":\"";
        int startIndex = json.IndexOf(searchPattern);
        if (startIndex == -1) return "";

        startIndex += searchPattern.Length;
        int endIndex = json.IndexOf('"', startIndex);
        if (endIndex == -1) return "";

        return json.Substring(startIndex, endIndex - startIndex);
    }

    private void UpdateAvatarInfoDisplay(int index, string avatarName, string author)
    {
        if (avatarInfoDisplays[index] == null) return;

        // Try to find TextMeshPro components to update
        TextMeshProUGUI[] textComponents = avatarInfoDisplays[index].GetComponentsInChildren<TextMeshProUGUI>();

        if (textComponents.Length > 0)
        {
            // First text component shows avatar name
            textComponents[0].text = avatarName;

            // Second text component shows author (if available)
            if (textComponents.Length > 1)
            {
                textComponents[1].text = $"by {author}";
            }
        }
    }

    public void Reload()
    {
        isWaiting = false;
        VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
    }
}
