﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using VRC.SDK3.StringLoading;
using VRC.Udon.Common.Interfaces;
using System.Collections;
using VRC.SDK3.Components;

public class StringAvatarPedestal : UdonSharpBehaviour
{
    public VRCAvatarPedestal[] avatarPedestals;
    public float updateInterval = 30f;

    [SerializeField]
    private VRCUrl mainUrl;

    private bool downloaded = false;
    private bool isWaiting = false;

    public override void OnPlayerJoined(VRCPlayerApi player)
    {
        if (!downloaded)
        {
            VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
            downloaded = true;
        }
    }

    public override void OnStringLoadSuccess(IVRCStringDownload result)
    {
        string[] lines = result.Result.Split('\n');
        for (int i = 0; i < avatarPedestals.Length && i < lines.Length; i++)
        {
            string[] parts = lines[i].Split(' ');
            if (parts.Length > 1)
            {
                avatarPedestals[i].blueprintId = parts[1].Trim();
            }
        }

        if (!isWaiting)
        {
            isWaiting = true;
            SendCustomEventDelayedSeconds(nameof(Reload), updateInterval);
        }
    }

    public void Reload()
    {
        isWaiting = false;
        VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
    }
}
