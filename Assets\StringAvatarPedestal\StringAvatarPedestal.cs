﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using TMPro;
using VRC.SDK3.StringLoading;
using VRC.Udon.Common.Interfaces;
using System.Collections;
using VRC.SDK3.Components;

public enum PlacementDirection
{
    PositiveX,
    NegativeX,
    PositiveZ,
    NegativeZ,
    Grid
}

public class StringAvatarPedestal : UdonSharpBehaviour
{
    [Header("Pedestal Configuration")]
    public GameObject avatarPedestalPrefab;
    public PlacementDirection placementDirection = PlacementDirection.PositiveX;
    public float pedestalSpacing = 2f;
    public bool showAvatarInfo = true;

    [Header("Grid Layout (only for Grid direction)")]
    public int gridWidth = 10;
    public float gridSpacingZ = 2f;

    [Header("Performance Settings")]
    public int chunkSize = 5;
    public float chunkDelay = 0.1f;

    [Header("Update Settings")]
    public float updateInterval = 30f;

    [Header("Data Source")]
    [SerializeField]
    private VRCUrl mainUrl = new VRCUrl("https://gist.githubusercontent.com/TheZiver/bb99f9facb8d14fd607dbb79e9a99d83/raw");

    [Header("Display Options")]
    public GameObject avatarInfoPrefab;
    public Transform pedestalParent;

    // Runtime variables
    private VRCAvatarPedestal[] avatarPedestals;
    private GameObject[] avatarInfoDisplays;
    private string[] avatarData; // Store parsed avatar data
    private bool downloaded = false;
    private bool isWaiting = false;
    private bool pedestalsCreated = false;
    private int totalAvatars = 0;
    private int currentChunk = 0;

    void Start()
    {
        LoadAvatarData();
    }

    private void LoadAvatarData()
    {
        if (!downloaded)
        {
            VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
            downloaded = true;
        }
    }

    public override void OnPlayerJoined(VRCPlayerApi player)
    {
        if (!downloaded)
        {
            VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
            downloaded = true;
        }
    }

    private void CreateAvatarPedestals()
    {
        if (pedestalsCreated || avatarPedestalPrefab == null || totalAvatars == 0)
            return;

        // Initialize arrays based on actual avatar count
        avatarPedestals = new VRCAvatarPedestal[totalAvatars];
        avatarData = new string[totalAvatars];
        if (showAvatarInfo && avatarInfoPrefab != null)
        {
            avatarInfoDisplays = new GameObject[totalAvatars];
        }

        // Set parent if not specified
        if (pedestalParent == null)
            pedestalParent = this.transform;

        // Create pedestals based on placement direction
        for (int i = 0; i < totalAvatars; i++)
        {
            Vector3 position = CalculatePedestalPosition(i);

            // Instantiate pedestal
            GameObject pedestalObj = Instantiate(avatarPedestalPrefab, position, pedestalParent.rotation);
            pedestalObj.transform.SetParent(pedestalParent);
            pedestalObj.name = $"AvatarPedestal_{i}";

            // Get the VRCAvatarPedestal component
            avatarPedestals[i] = pedestalObj.GetComponent<VRCAvatarPedestal>();

            // Create info display if enabled
            if (showAvatarInfo && avatarInfoPrefab != null)
            {
                Vector3 infoPosition = position + new Vector3(0, 2f, 0);
                GameObject infoObj = Instantiate(avatarInfoPrefab, infoPosition, pedestalParent.rotation);
                infoObj.transform.SetParent(pedestalParent);
                infoObj.name = $"AvatarInfo_{i}";
                avatarInfoDisplays[i] = infoObj;
            }
        }

        pedestalsCreated = true;
    }

    private Vector3 CalculatePedestalPosition(int index)
    {
        Vector3 basePosition = pedestalParent.position;

        switch (placementDirection)
        {
            case PlacementDirection.PositiveX:
                return basePosition + new Vector3(index * pedestalSpacing, 0, 0);

            case PlacementDirection.NegativeX:
                return basePosition + new Vector3(-index * pedestalSpacing, 0, 0);

            case PlacementDirection.PositiveZ:
                return basePosition + new Vector3(0, 0, index * pedestalSpacing);

            case PlacementDirection.NegativeZ:
                return basePosition + new Vector3(0, 0, -index * pedestalSpacing);

            case PlacementDirection.Grid:
                int row = index / gridWidth;
                int col = index % gridWidth;
                return basePosition + new Vector3(col * pedestalSpacing, 0, row * gridSpacingZ);

            default:
                return basePosition + new Vector3(index * pedestalSpacing, 0, 0);
        }
    }

    private int CountAvatarsInJson(string jsonData)
    {
        // Clean up the JSON data
        string cleanedJson = jsonData.Replace("\\_", "_").Replace("\\\"", "\"");

        // Find community_avatars array
        string[] searchPatterns = {
            "\"community_avatars\":[",
            "\"community_avatars\": [",
            "community_avatars\":[",
            "community_avatars\": ["
        };

        int startIndex = -1;
        string foundPattern = "";

        foreach (string pattern in searchPatterns)
        {
            startIndex = cleanedJson.IndexOf(pattern);
            if (startIndex != -1)
            {
                foundPattern = pattern;
                break;
            }
        }

        if (startIndex == -1) return 0;

        // Count objects in the array
        startIndex += foundPattern.Length;
        int count = 0;
        int currentPos = startIndex;

        while (currentPos < cleanedJson.Length)
        {
            int objectStart = cleanedJson.IndexOf('{', currentPos);
            if (objectStart == -1) break;

            // Find the end of this object
            int bracketCount = 1;
            int objectEnd = objectStart + 1;
            for (int i = objectStart + 1; i < cleanedJson.Length && bracketCount > 0; i++)
            {
                if (cleanedJson[i] == '{') bracketCount++;
                else if (cleanedJson[i] == '}') bracketCount--;
                objectEnd = i;
            }

            if (bracketCount != 0) break;

            count++;
            currentPos = objectEnd + 1;

            // Check if we've reached the end of the array
            int nextComma = cleanedJson.IndexOf(',', currentPos);
            int arrayEnd = cleanedJson.IndexOf(']', currentPos);
            if (arrayEnd != -1 && (nextComma == -1 || arrayEnd < nextComma))
                break;
        }

        return count;
    }

    public override void OnStringLoadSuccess(IVRCStringDownload result)
    {
        if (result.Result != null)
        {
            // First count how many avatars we have
            totalAvatars = CountAvatarsInJson(result.Result);

            if (totalAvatars > 0)
            {
                // Create pedestals based on actual avatar count
                if (!pedestalsCreated)
                {
                    CreateAvatarPedestals();
                }

                // Parse and store avatar data
                ParseJsonAndStoreAvatars(result.Result);

                // Start chunked loading
                currentChunk = 0;
                SendCustomEventDelayedSeconds(nameof(LoadNextChunk), chunkDelay);
            }
        }

        if (!isWaiting)
        {
            isWaiting = true;
            SendCustomEventDelayedSeconds(nameof(Reload), updateInterval);
        }
    }

    public override void OnStringLoadError(IVRCStringDownload result)
    {
        Debug.LogError($"Failed to load JSON data: {result.Error}");
        Debug.LogError($"Error code: {result.ErrorCode}");
    }

    private void ParseJsonAndStoreAvatars(string jsonData)
    {
        if (avatarData == null || avatarData.Length == 0)
            return;

        // Clean up the JSON data
        string cleanedJson = jsonData.Replace("\\_", "_").Replace("\\\"", "\"");

        // Find community_avatars array
        string[] searchPatterns = {
            "\"community_avatars\":[",
            "\"community_avatars\": [",
            "community_avatars\":[",
            "community_avatars\": ["
        };

        int startIndex = -1;
        string foundPattern = "";

        foreach (string pattern in searchPatterns)
        {
            startIndex = cleanedJson.IndexOf(pattern);
            if (startIndex != -1)
            {
                foundPattern = pattern;
                break;
            }
        }

        if (startIndex == -1) return;

        // Move to the start of the array content
        startIndex += foundPattern.Length;

        // Parse and store avatar data
        int avatarIndex = 0;
        int currentPos = startIndex;

        while (currentPos < cleanedJson.Length && avatarIndex < avatarData.Length)
        {
            // Find the start of the next avatar object
            int objectStart = cleanedJson.IndexOf('{', currentPos);
            if (objectStart == -1) break;

            // Find the end of this avatar object
            int bracketCount = 1;
            int objectEnd = objectStart + 1;
            for (int i = objectStart + 1; i < cleanedJson.Length && bracketCount > 0; i++)
            {
                if (cleanedJson[i] == '{') bracketCount++;
                else if (cleanedJson[i] == '}') bracketCount--;
                objectEnd = i;
            }

            if (bracketCount != 0) break;

            // Store the avatar object data
            avatarData[avatarIndex] = cleanedJson.Substring(objectStart, objectEnd - objectStart + 1);

            avatarIndex++;
            currentPos = objectEnd + 1;
        }
    }

    public void LoadNextChunk()
    {
        if (avatarData == null || currentChunk * chunkSize >= avatarData.Length)
            return;

        int startIndex = currentChunk * chunkSize;
        int endIndex = Mathf.Min(startIndex + chunkSize, avatarData.Length);

        for (int i = startIndex; i < endIndex; i++)
        {
            if (!string.IsNullOrEmpty(avatarData[i]))
            {
                LoadSingleAvatar(i, avatarData[i]);
            }
        }

        currentChunk++;

        // Schedule next chunk if there are more avatars
        if (currentChunk * chunkSize < avatarData.Length)
        {
            SendCustomEventDelayedSeconds(nameof(LoadNextChunk), chunkDelay);
        }
    }

    private void LoadSingleAvatar(int index, string avatarObject)
    {
        if (avatarPedestals == null || index >= avatarPedestals.Length || avatarPedestals[index] == null)
            return;

        // Parse the avatar data
        string avatarId = ExtractJsonValue(avatarObject, "avatar_id");
        string avatarName = ExtractJsonValue(avatarObject, "avatar_name");
        string author = ExtractJsonValue(avatarObject, "author");

        // Assign to pedestal
        if (!string.IsNullOrEmpty(avatarId))
        {
            avatarPedestals[index].blueprintId = avatarId;

            // Update info display if available
            if (showAvatarInfo && avatarInfoDisplays != null && avatarInfoDisplays[index] != null)
            {
                UpdateAvatarInfoDisplay(index, avatarName, author);
            }
        }
    }



    private string ExtractJsonValue(string json, string key)
    {
        // Try multiple patterns for the key
        string[] patterns = {
            $"\"{key}\":\"",
            $"\"{key}\": \"",
            $"\"{key}\" :\"",
            $"\"{key}\" : \"",
            $"{key}\":\"",
            $"{key}\": \""
        };

        foreach (string pattern in patterns)
        {
            int startIndex = json.IndexOf(pattern);
            if (startIndex != -1)
            {
                startIndex += pattern.Length;
                int endIndex = json.IndexOf('"', startIndex);
                if (endIndex != -1)
                {
                    string value = json.Substring(startIndex, endIndex - startIndex);
                    // Clean up escaped characters
                    value = value.Replace("\\_", "_").Replace("\\\"", "\"");
                    return value;
                }
            }
        }

        return "";
    }

    private void UpdateAvatarInfoDisplay(int index, string avatarName, string author)
    {
        if (avatarInfoDisplays[index] == null) return;

        // Try to find TextMeshPro components to update
        TextMeshProUGUI[] textComponents = avatarInfoDisplays[index].GetComponentsInChildren<TextMeshProUGUI>();

        if (textComponents.Length > 0)
        {
            // First text component shows avatar name
            textComponents[0].text = avatarName;

            // Second text component shows author (if available)
            if (textComponents.Length > 1)
            {
                textComponents[1].text = $"by {author}";
            }
        }
    }

    public void Reload()
    {
        isWaiting = false;
        VRCStringDownloader.LoadUrl(mainUrl, (IUdonEventReceiver)this);
    }
}
