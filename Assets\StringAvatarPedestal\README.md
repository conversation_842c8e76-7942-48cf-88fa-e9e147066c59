# JSON Avatar Pedestal System

This UdonSharp script creates a dynamic avatar pedestal system that loads avatar data from a JSON API and automatically generates avatar pedestals in a row.

## Quick Start

1. Create a VRCAvatarPedestal prefab and assign it to the script
2. The script is pre-configured to load Fish Avatars community data
3. Add the script to a GameObject in your scene
4. The system will automatically create 50 avatar pedestals in a row and load the latest community avatars

## Detailed Setup

## Features

- **JSON Data Loading**: Parses JSON data with the `community_avatars` structure
- **Automatic Pedestal Generation**: Creates avatar pedestals dynamically at runtime
- **Row Layout**: Arranges pedestals in a straight line with configurable spacing
- **Avatar Info Display**: Optional text displays showing avatar names and authors
- **Automatic Updates**: Periodically reloads avatar data from the remote URL

## Setup Instructions

### 1. Prefab Requirements

You need to create two prefabs:

#### Avatar Pedestal Prefab
- Create a GameObject with a `VRCAvatarPedestal` component
- Save it as a prefab (e.g., "AvatarPedestalPrefab")

#### Avatar Info Prefab (Optional)
- Create a GameObject with one or more `TextMeshProUGUI` components
- First text component will show the avatar name
- Second text component will show the author
- Position the text above where the pedestal will be
- Save it as a prefab (e.g., "AvatarInfoPrefab")

### 2. Script Configuration

Add the `StringAvatarPedestal` script to a GameObject in your scene and configure:

#### Pedestal Configuration
- **Avatar Pedestal Prefab**: Assign your avatar pedestal prefab
- **Max Avatars**: Maximum number of avatars to display (default: 50)
- **Pedestal Spacing**: Distance between pedestals in Unity units (default: 2.0)
- **Show Avatar Info**: Enable/disable avatar name and author display

#### Update Settings
- **Update Interval**: How often to reload data in seconds (default: 30)

#### Data Source
- **Main Url**: URL that returns JSON data in the expected format (defaults to the Fish Avatars community data)

#### Display Options
- **Avatar Info Prefab**: Assign your avatar info prefab (optional)
- **Pedestal Parent**: Transform to parent the pedestals under (optional, uses script's transform if not set)

### 3. JSON Data Format

The script is pre-configured to load from the Fish Avatars community data:
`https://gist.githubusercontent.com/TheZiver/bb99f9facb8d14fd607dbb79e9a99d83/raw`

The script expects JSON data in this format:

```json
{
  "community_avatars": [
    {
      "avatar_name": "Avatar Name",
      "author": "Author Name",
      "avatar_id": "avtr_xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx",
      "avatar_link": "https://vrchat.com/home/<USER>/avtr_...",
      "avatar_image_url": "https://api.vrchat.cloud/api/1/file/...",
      "created_at": "2025-06-15T00:51:15.206000+00:00",
      "tags": ["TAG1", "TAG2"]
    }
  ]
}
```

Required fields:
- `avatar_id`: The VRChat avatar blueprint ID
- `avatar_name`: Display name for the avatar
- `author`: Creator of the avatar

### 4. How It Works

1. **Initialization**: On Start(), the script creates the specified number of avatar pedestals in a row
2. **Data Loading**: When a player joins, it downloads JSON data from the specified URL
3. **Parsing**: The script parses the JSON and extracts avatar information
4. **Assignment**: Avatar IDs are assigned to pedestals, and info displays are updated
5. **Updates**: The system automatically reloads data at the specified interval

### 5. Layout

Pedestals are arranged in a straight line along the X-axis:
- First pedestal at the parent's position
- Each subsequent pedestal offset by `pedestalSpacing` units
- Info displays positioned 2 units above each pedestal

### 6. Troubleshooting

- **No pedestals appear**: Check that the Avatar Pedestal Prefab is assigned and has a VRCAvatarPedestal component
- **JSON parsing fails**: Verify the JSON format matches the expected structure
- **Info displays don't update**: Ensure the Avatar Info Prefab has TextMeshProUGUI components
- **Pedestals not loading avatars**: Check that the JSON contains valid `avatar_id` fields

### 7. Performance Notes

- The script creates all pedestals at startup for better performance
- JSON parsing is done manually to work within UdonSharp limitations
- Consider limiting `maxAvatars` based on your world's performance requirements

## Example Usage

1. Create an avatar pedestal prefab with VRCAvatarPedestal component
2. Create an info display prefab with TextMeshProUGUI components
3. Add StringAvatarPedestal script to an empty GameObject
4. Configure the script with your prefabs and JSON URL
5. The system will automatically create and manage the avatar display
