using UnityEngine;
using VRC.SDK3.Components;
using TMPro;

#if UNITY_EDITOR
using UnityEditor;

/// <summary>
/// Helper script to create example prefabs for the StringAvatarPedestal system.
/// This script is only available in the Unity Editor.
/// </summary>
public class ExampleSetup : MonoBehaviour
{
    [Header("Prefab Creation")]
    [Tooltip("Click to create an example avatar pedestal prefab")]
    public bool createAvatarPedestalPrefab = false;
    
    [<PERSON>lt<PERSON>("Click to create an example avatar info display prefab")]
    public bool createAvatarInfoPrefab = false;

    void OnValidate()
    {
        if (createAvatarPedestalPrefab)
        {
            createAvatarPedestalPrefab = false;
            CreateAvatarPedestalPrefab();
        }

        if (createAvatarInfoPrefab)
        {
            createAvatarInfoPrefab = false;
            CreateAvatarInfoPrefab();
        }
    }

    private void CreateAvatarPedestalPrefab()
    {
        // Create the main pedestal GameObject
        GameObject pedestalObj = new GameObject("AvatarPedestalPrefab");
        
        // Add VRCAvatarPedestal component
        VRCAvatarPedestal pedestal = pedestalObj.AddComponent<VRCAvatarPedestal>();
        
        // Create a simple visual representation
        GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        visual.transform.SetParent(pedestalObj.transform);
        visual.transform.localPosition = Vector3.zero;
        visual.transform.localScale = new Vector3(1f, 0.1f, 1f);
        visual.name = "PedestalBase";
        
        // Create a platform for the avatar
        GameObject platform = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        platform.transform.SetParent(pedestalObj.transform);
        platform.transform.localPosition = new Vector3(0, 0.1f, 0);
        platform.transform.localScale = new Vector3(0.8f, 0.05f, 0.8f);
        platform.name = "AvatarPlatform";
        
        // Set a different material color for the platform
        Renderer platformRenderer = platform.GetComponent<Renderer>();
        Material platformMat = new Material(Shader.Find("Standard"));
        platformMat.color = Color.cyan;
        platformRenderer.material = platformMat;

        // Save as prefab
        string prefabPath = "Assets/StringAvatarPedestal/AvatarPedestalPrefab.prefab";
        PrefabUtility.SaveAsPrefabAsset(pedestalObj, prefabPath);
        
        // Clean up the scene object
        DestroyImmediate(pedestalObj);
        
        Debug.Log($"Created Avatar Pedestal Prefab at: {prefabPath}");
        
        // Select the created prefab
        Selection.activeObject = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
    }

    private void CreateAvatarInfoPrefab()
    {
        // Create the main info display GameObject
        GameObject infoObj = new GameObject("AvatarInfoPrefab");
        
        // Create canvas for UI elements
        Canvas canvas = infoObj.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = null; // Will use main camera
        
        // Set canvas size
        RectTransform canvasRect = canvas.GetComponent<RectTransform>();
        canvasRect.sizeDelta = new Vector2(200, 100);
        canvasRect.localScale = new Vector3(0.01f, 0.01f, 0.01f); // Scale down for world space

        // Create avatar name text
        GameObject nameTextObj = new GameObject("AvatarNameText");
        nameTextObj.transform.SetParent(infoObj.transform);
        
        TextMeshProUGUI nameText = nameTextObj.AddComponent<TextMeshProUGUI>();
        nameText.text = "Avatar Name";
        nameText.fontSize = 24;
        nameText.color = Color.white;
        nameText.alignment = TextAlignmentOptions.Center;
        
        RectTransform nameRect = nameTextObj.GetComponent<RectTransform>();
        nameRect.anchorMin = new Vector2(0, 0.5f);
        nameRect.anchorMax = new Vector2(1, 1);
        nameRect.offsetMin = Vector2.zero;
        nameRect.offsetMax = Vector2.zero;

        // Create author text
        GameObject authorTextObj = new GameObject("AuthorText");
        authorTextObj.transform.SetParent(infoObj.transform);
        
        TextMeshProUGUI authorText = authorTextObj.AddComponent<TextMeshProUGUI>();
        authorText.text = "by Author";
        authorText.fontSize = 18;
        authorText.color = Color.gray;
        authorText.alignment = TextAlignmentOptions.Center;
        
        RectTransform authorRect = authorTextObj.GetComponent<RectTransform>();
        authorRect.anchorMin = new Vector2(0, 0);
        authorRect.anchorMax = new Vector2(1, 0.5f);
        authorRect.offsetMin = Vector2.zero;
        authorRect.offsetMax = Vector2.zero;

        // Save as prefab
        string prefabPath = "Assets/StringAvatarPedestal/AvatarInfoPrefab.prefab";
        PrefabUtility.SaveAsPrefabAsset(infoObj, prefabPath);
        
        // Clean up the scene object
        DestroyImmediate(infoObj);
        
        Debug.Log($"Created Avatar Info Prefab at: {prefabPath}");
        
        // Select the created prefab
        Selection.activeObject = AssetDatabase.LoadAssetAtPath<GameObject>(prefabPath);
    }
}

#endif
